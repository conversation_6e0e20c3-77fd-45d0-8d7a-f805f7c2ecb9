import { useRouter } from "@tarojs/taro";
import { useEffect } from "react"
import { getHabitsStatistics } from "src/services/habits"

const HabitDetail = () => {
  const router = useRouter();
  const { id } = router.params;
  console.log(id)

  useEffect(() => {
    console.log('habit detail')
    getHabitsStatistics(id).then((res) => {
      console.log(res.data)
    })
  }, [])
  return <></>
}

export default HabitDetail