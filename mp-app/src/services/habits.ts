import request from '../utils/request';

interface CreateHabitParams {
  name: string
  icon: string
  theme_color: string
  description: string
  frequency: number[]
  preferred_time: string
  status: 'active' | 'archived'
}

export const getHabitsLibrary = () => {  
  return request({
    url: '/habits/library/categories/tree',
    method: 'GET',
  });
};

export const createHabit = (params: CreateHabitParams) => {
  return request({
    url: '/habits/my-habits',
    method: 'POST',
    data: params,
  });
};

export const getHomeHabitsOverview = () => {
  return request({
    url: '/habits/statistics/overview',
    method: 'GET',
  });
};



export const getHabitsStatistics = (id?: string) => {
  return request({
    url: `/habits/statistics/habit/${id}`,
    method: 'GET',
  });
};